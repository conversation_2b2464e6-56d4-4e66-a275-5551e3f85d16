[package]
name = "benches"
publish = false
version = "0.0.0"
edition = { workspace = true }
repository = { workspace = true }
license = { workspace = true }

[lints]
workspace = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]

[dev-dependencies]
criterion = { version = "0.5.0", features = [ "async_tokio", "html_reports" ] }
dfir_rs = { path = "../dfir_rs", features = [ "debugging" ] }
nameof = "1.0.0"
futures = "0.3"
rand = "0.8.0"
rand_distr = "0.4.3"
seq-macro = "0.2.0"
timely = { package = "timely-master", version = "0.13.0-dev.1" }
differential-dataflow = { package = "differential-dataflow-master", version = "0.13.0-dev.1" } # git = "https://github.com/TimelyDataflow/differential-dataflow.git", rev = "7bc5338a977fe1d95b96a9ba84ba8cd460e0cdd7" } # "0.12"
tokio = { version = "1.29.0", features = [ "rt-multi-thread" ] }
static_assertions = "1.0.0"

[[bench]]
name = "arithmetic"
harness = false

[[bench]]
name = "fan_in"
harness = false

[[bench]]
name = "fan_out"
harness = false

[[bench]]
name = "fork_join"
harness = false

[[bench]]
name = "identity"
harness = false

[[bench]]
name = "upcase"
harness = false

[[bench]]
name = "join"
harness = false

[[bench]]
name = "reachability"
harness = false

[[bench]]
name = "micro_ops"
harness = false

[[bench]]
name = "symmetric_hash_join"
harness = false

[[bench]]
name = "words_diamond"
harness = false

[[bench]]
name = "futures"
harness = false