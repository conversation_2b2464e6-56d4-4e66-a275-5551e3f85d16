use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize, Debug, Eq, PartialEq, Hash)]
pub enum ClientClass {
    Basic,
    Prime,
}

#[derive(PartialEq, Eq, Clone, Hash, Serialize, Deserialize, Debug, Default)]
pub struct LineItem {
    pub name: String,
    pub qty: i16,
}

#[derive(PartialEq, Eq, Clone, Serialize, Deserialize, Debug)]
pub struct ClLineItem {
    pub client: usize,
    pub li: LineItem,
}

#[derive(PartialEq, Eq, Clone, Hash, Serialize, Deserialize, Debug)]
pub enum Request {
    ClLineItem { client: usize, li: LineItem },
    Checkout { client: usize },
}
