use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON><PERSON><PERSON>, Eq, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize, Debug, <PERSON>h, Copy)]
pub enum MsgType {
    Prepare,
    Commit,
    Abort,
    AckP2,
    End,
    Ended,
    Err,
}

#[derive(PartialEq, Eq, Clone, Serialize, Deserialize, Debug)]
pub struct CoordMsg {
    pub xid: u16,
    pub mtype: MsgType,
}
/// Member Response
#[derive(PartialEq, Eq, Clone, Serialize, Deserialize, Debug)]
pub struct SubordResponse {
    pub xid: u16,
    pub mtype: MsgType,
}
