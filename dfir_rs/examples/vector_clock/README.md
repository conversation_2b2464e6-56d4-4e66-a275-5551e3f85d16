Simple echo server with vector-clock stamped messages.

To run the example and see non-trivial vector clocks, open 3 terminals.

In one terminal run the server like so:
```
cargo run -p hydroflow --example vector_clock -- --role server --addr localhost:12347
```

In another terminal run a client:
```
cargo run -p hydroflow --example vector_clock -- --role client --server-addr localhost:12347
```

In a third terminal run a client:
```
cargo run -p hydroflow --example vector_clock -- --role client --server-addr localhost:12347
```

If you type in a client terminal the message will be sent to the server, echo'd back to the client and printed with a vector clock. By typing in both clients you should generate vector clocks with entries for both clients and the server.

Adding the `--graph <graph_type>` flag to the end of the command lines above will print out a node-and-edge diagram of the program. Supported values for `<graph_type>` include [mermaid](https://mermaid-js.github.io/) and [dot](https://graphviz.org/doc/info/lang.html).
