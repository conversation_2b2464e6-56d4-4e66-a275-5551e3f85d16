[package]
name = "dfir_macro"
publish = true
version = "0.13.0"
documentation = "https://docs.rs/dfir_macro/"
description = "Macro for using Hydro's Data Flow Intermediate Representation (DFIR)."
edition = { workspace = true }
repository = { workspace = true }
license = { workspace = true }

[lints]
workspace = true

[lib]
proc-macro = true

[dependencies]
# Note: If we ever compile this proc macro crate to WASM (e.g., if we are
# building on a WASM host), we may need to turn diagnostics off for WASM if
# proc_macro2 still does not support WASM.
dfir_lang = { path = "../dfir_lang", version = "^0.13.0" }
proc-macro2 = "1.0.95"
proc-macro-crate = "1.0.0"
quote = "1.0.35"
syn = { version = "2.0.46", features = [ "parsing", "extra-traits" ] }

[build-dependencies]
dfir_lang = { path = "../dfir_lang", version = "^0.13.0" }
itertools = "0.13.0"
quote = "1.0.35"
rustc_version = "0.4.0"
