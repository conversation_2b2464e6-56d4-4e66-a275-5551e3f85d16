[package]
description = "Gossip KV Library"
name = "gossip_kv"
version = "0.1.0"
publish = false
edition = { workspace = true }
repository = { workspace = true }
license = { workspace = true }

[lints]
workspace = true

[dependencies]
clap = { version = "4.5.4", features = ["derive", "env"] }
config = "0.15.0"
governor = "0.7.0"
hostname = "0.4.0"
dfir_rs = { path= "../../dfir_rs" }
lattices = { path = '../../lattices'}
lazy_static = "1.5.0"
# The specific set of features for Notify are picked to disable the default cross-beam channels (cause problems with
# tokio) and use std channels. See docs for more information: https://docs.rs/notify/6.1.1/notify/
notify = { version = "6.1.1", default-features = false, features = ["macos_kqueue"] }
prometheus = "0.13.4"
rand = "0.8.5"
serde = "1.0.203"
serde_json = "1.0.117"
shlex = "1.3.0"
tokio = { version = "1.0.0", features = ["rt", "rt-multi-thread", "macros"] }
tracing = "0.1.40"
tracing-subscriber = {version = "0.3.18", features = ["env-filter"]}
uuid = { version = "1.9.1", features = ["v4"] }
warp = "0.3.7"

[[bin]]
name = "gossip_server"
path = "server/main.rs"

[[bin]]
name = "load_test_server"
path = "load_test_server/server.rs"

[[bin]]
name = "gossip_cli"
path = "cli/main.rs"

[lib]
name = "gossip_kv"
path = "kv/lib.rs"
