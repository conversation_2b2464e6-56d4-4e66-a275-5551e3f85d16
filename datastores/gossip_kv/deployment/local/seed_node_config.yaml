apiVersion: v1
kind: ConfigMap
metadata:
  name: gossip-kv-dynamic-config
data:
  dynamic.toml: |
    [[seed_nodes]]
    id = "gossip-kv-seed-nodes-0"
    address = "gossip-kv-seed-nodes-0.gossip-kv-seed-nodes.default.svc.cluster.local:3000"
    
    [[seed_nodes]]
    id = "gossip-kv-seed-nodes-1"
    address = "gossip-kv-seed-nodes-1.gossip-kv-seed-nodes.default.svc.cluster.local:3000"
    
    [[seed_nodes]]
    id = "gossip-kv-seed-nodes-2"
    address = "gossip-kv-seed-nodes-2.gossip-kv-seed-nodes.default.svc.cluster.local:3000"