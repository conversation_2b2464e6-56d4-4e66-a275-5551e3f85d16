use hydro_lang::*;
use akd::{Akd<PERSON>abel, AkdValue, LookupProof};
use serde::{Serialize, Deserialize};

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct PublishMsg {
    pub updates: Vec<(Akd<PERSON>abel, AkdValue)>,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct PublishMsgResponse {
    pub epoch: u64,
    pub root_hash: Vec<u8>,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct LookupMsg {
    pub label: AkdLabel,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct LookupMsgResponse {
    pub lookup_proof: LookupProof,
    pub epoch_hash: Vec<u8>,
}

pub struct Server {}

pub struct Clients {}

// API
    // Publish: publish client ID, public key
    // - label: Akd<PERSON><PERSON>l (client identifier)
    // - value: AkdValue (public key data)
    // - epoch: u64 (optional version number)

    // Lookup: verify mapping
    // - label: Akd<PERSON><PERSON>l (client identifier to lookup)
    // - epoch: u64 (specific version, or latest if not specified)
    // - Returns: LookupProof for verification

    // History: verify history of mappings
    // - label: AkdLabel (client identifier)
    // - history_params: HistoryParams (epoch range, limit)
    // - Returns: Vec<HistoryProof> with historical values

pub fn akd_server<'a>(flow: &FlowBuilder<'a>) -> (Process<'a, Server>, Cluster<'a, Clients>) {
    // generate clients and commands
    let clients = flow.cluster::<Clients>();
    // Assume single server.
    let server = flow.process::<Server>();

    let client_publish_req = clients
        .source_iter(q!([PublishMsg {
            updates: vec![(
                AkdLabel::from(format!("{}", CLUSTER_SELF_ID.raw_id).as_str()),
                AkdValue::from("AAABBBCCC"),
            )],
        }]))
        .send_bincode(&server)
        .inspect(q!(|(id, msg)| println!(
            "...publishing {} entries from client #{}...",
            msg.updates.len(), id
        )));

    let server_publish_resp = client_publish_req.send_bincode(&server);

    server_publish_resp
        .fold

    // Add a simple response from server back to clients
    server_publish_resp
        .map(q!(|(id, msg)| (id, PublishMsgResponse {
            epoch: 1,
            root_hash: vec![0, 1, 2, 3] // Dummy hash for now
        })))
        .broadcast_bincode(&clients)
        .for_each(q!(|(id, resp)| println!(
            "Client #{} received response: epoch={}, hash={:?}",
            id, resp.epoch, resp.root_hash
        )));

    (server, clients)
}