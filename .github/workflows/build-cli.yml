name: Build CLI

on:
  push:
    branches:
      - main
      - feature/**
    tags:
      - "hydro_cli-v[0-9]+.[0-9]+.[0-9]+"

env:
  PACKAGE_NAME: hydro_deploy
  PYTHON_VERSION: "3.10" # to build abi3 wheels

# based on <PERSON><PERSON>'s CI
jobs:
  pre_job:
    runs-on: ubuntu-latest
    outputs:
      should_skip: ${{ steps.skip_check.outputs.should_skip }}
    steps:
      - id: skip_check
        uses: fkirc/skip-duplicate-actions@v5.3.1
        with:
          cancel_others: "true"

  macos-universal:
    runs-on: macos-latest
    if: ${{ needs.pre_job.outputs.should_skip != 'true' }}
    needs: pre_job
    strategy:
      matrix:
        target:
          - universal2-apple-darwin
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}
      - name: "Build wheels"
        uses: PyO3/maturin-action@v1
        with:
          working-directory: hydro_deploy/hydro_cli
          target: ${{ matrix.target }}
          args: --release --out dist
        env:
          CARGO_TARGET_X86_64_APPLE_DARWIN_LINKER: clang
          CARGO_TARGET_AARCH64_APPLE_DARWIN_LINKER: clang
      - name: "Install built wheel"
        run: |
          pip install hydro_deploy/hydro_cli/dist/${{ env.PACKAGE_NAME }}-*.whl --force-reinstall
      - name: "Upload wheels"
        uses: actions/upload-artifact@v4
        with:
          name: wheels-${{ matrix.target }}
          path: hydro_deploy/hydro_cli/dist

  linux:
    runs-on: ubuntu-latest
    if: ${{ needs.pre_job.outputs.should_skip != 'true' }}
    needs: pre_job
    strategy:
      matrix:
        target:
          - x86_64-unknown-linux-gnu
          - i686-unknown-linux-gnu
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          architecture: x64
      - name: "Build wheels"
        uses: PyO3/maturin-action@v1
        with:
          working-directory: hydro_deploy/hydro_cli
          target: ${{ matrix.target }}
          manylinux: auto
          # fix openssl, based on https://github.com/Intreecom/scyllapy/blob/develop/.github/workflows/release.yaml
          before-script-linux: |
            if command -v yum &> /dev/null; then
              yum update -y && yum install -y perl-core libatomic

              # If we're running on i686 we need to symlink libatomic
              # in order to build openssl with -latomic flag.
              if [[ ! -d "/usr/lib64" ]]; then
                ln -s /usr/lib/libatomic.so.1 /usr/lib/libatomic.so
              fi
            fi
          args: --release --out dist
      - name: "Install built wheel"
        if: ${{ startsWith(matrix.target, 'x86_64') }}
        run: |
          pip install hydro_deploy/hydro_cli/dist/${{ env.PACKAGE_NAME }}-*.whl --force-reinstall
      - name: "Upload wheels"
        uses: actions/upload-artifact@v4
        with:
          name: wheels-${{ matrix.target }}
          path: hydro_deploy/hydro_cli/dist

  linux-cross:
    runs-on: ubuntu-latest
    if: ${{ needs.pre_job.outputs.should_skip != 'true' }}
    needs: pre_job
    strategy:
      matrix:
        platform:
          - target: aarch64-unknown-linux-gnu
            arch: aarch64
          - target: armv7-unknown-linux-gnueabihf
            arch: armv7
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}
      - name: "Build wheels"
        uses: PyO3/maturin-action@v1
        env:
          # Workaround ring 0.17 build issue (kudos to uv)
          CFLAGS_aarch64_unknown_linux_gnu: "-D__ARM_ARCH=8"
        with:
          working-directory: hydro_deploy/hydro_cli
          target: ${{ matrix.platform.target }}
          manylinux: auto
          args: --release --out dist
      - name: "Upload wheels"
        uses: actions/upload-artifact@v4
        with:
          name: wheels-${{ matrix.platform.target }}
          path: hydro_deploy/hydro_cli/dist

  windows:
    runs-on: windows-latest
    if: ${{ needs.pre_job.outputs.should_skip != 'true' }}
    needs: pre_job
    strategy:
      matrix:
        platform:
          - target: x86_64-pc-windows-msvc
            arch: x64
          - target: i686-pc-windows-msvc
            arch: x86
          - target: aarch64-pc-windows-msvc
            arch: x64
        exclude:
          # aarch64 doesn't compile due to ring dependency
          - platform:
              target: aarch64-pc-windows-msvc
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          architecture: ${{ matrix.platform.arch }}
      - name: "Build wheels"
        uses: PyO3/maturin-action@v1
        with:
          maturin-version: 1.7.6
          working-directory: hydro_deploy/hydro_cli
          target: ${{ matrix.platform.target }}
          args: --release --out dist
      - name: "Install built wheel"
        if: ${{ !startsWith(matrix.platform.target, 'aarch64') }}
        shell: bash
        run: |
          python -m pip install hydro_deploy/hydro_cli/dist/${{ env.PACKAGE_NAME }}-*.whl --force-reinstall
      - name: "Upload wheels"
        uses: actions/upload-artifact@v4
        with:
          name: wheels-${{ matrix.platform.target }}
          path: hydro_deploy/hydro_cli/dist

  release:
    name: Release
    runs-on: ubuntu-latest
    needs:
      - macos-universal
      - linux
      - linux-cross
      - windows
    if: ${{ startsWith(github.ref, 'refs/tags/hydro_cli-') }}
    steps:
      - uses: actions/download-artifact@v3
        with:
          pattern: wheels-*
          path: wheels
          merge-multiple: true
      - uses: actions/setup-python@v5
      - name: "Publish to PyPi"
        env:
          TWINE_USERNAME: __token__
          TWINE_PASSWORD: ${{ secrets.PYPI_TOKEN }}
        run: |
          pip install --upgrade twine
          twine upload --skip-existing *
