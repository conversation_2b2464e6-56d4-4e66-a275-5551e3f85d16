name: Template

on:
  push:
    branches: [main]
  pull_request:
  schedule:
    - cron: "14 04 * * *" # Daily at 9:14 PM PDT, 8:14 PM PST.

jobs:
  pre_job:
    runs-on: ubuntu-latest
    outputs:
      should_skip: ${{ steps.skip_check.outputs.should_skip }}
    steps:
      - id: skip_check
        uses: fkirc/skip-duplicate-actions@v5.3.1
        with:
          cancel_others: "true"

  test_dfir:
    name: Test dfir
    needs: pre_job
    if: ${{ needs.pre_job.outputs.should_skip != 'true' || github.event_name != 'pull_request' }}
    timeout-minutes: 10
    runs-on: ubuntu-latest

    steps:
      - name: Checkout sources
        uses: actions/checkout@v4

      - name: Action cargo-generate
        uses: cargo-generate/cargo-generate-action@v0.20.0
        with:
          name: generated
          template: template/dfir
          arguments: "-d hydro_git=${{ github.event.pull_request.head.repo.clone_url }} -d hydro_branch=${{ github.event.pull_request.head.ref }}"
      - name: Move generated project
        run: |
          mv generated ${{ runner.temp }}/

      - run: cargo check --manifest-path "${{ runner.temp }}/generated/Cargo.toml" --all-targets
      - run: cargo fmt --manifest-path "${{ runner.temp }}/generated/Cargo.toml"  --all -- --check
      - run: cargo clippy --manifest-path "${{ runner.temp }}/generated/Cargo.toml"  --all --all-targets -- -D warnings
      - run: cargo test --manifest-path "${{ runner.temp }}/generated/Cargo.toml"  --all-targets --no-fail-fast
      - run: cargo build --manifest-path "${{ runner.temp }}/generated/Cargo.toml"  --all-targets

      - name: test template example
        run: |
          ECHO_STRING='hello this is a test'

          trap 'rm client-input && rm client-output && kill $(jobs -p)' EXIT

          echo "$ECHO_STRING" >client-input

          "${{ runner.temp }}/generated/target/debug/dfir-template" --role server --address 127.0.0.100:2048 &
          "${{ runner.temp }}/generated/target/debug/dfir-template" --role client --address 127.0.0.100:2048 <client-input >client-output &

          sleep 1

          if cat client-output | grep -q "$ECHO_STRING"; then
              exit 0
          else
              exit -1
          fi

  test_hydro_lang:
    name: Test hydro_lang
    needs: pre_job
    if: ${{ needs.pre_job.outputs.should_skip != 'true' || github.event_name != 'pull_request' }}
    timeout-minutes: 10
    runs-on: ubuntu-latest

    steps:
      - name: Checkout sources
        uses: actions/checkout@v4

      - name: Action cargo-generate
        uses: cargo-generate/cargo-generate-action@v0.20.0
        with:
          name: generated
          template: template/hydro
          arguments: "-d hydro_git=${{ github.event.pull_request.head.repo.clone_url }} -d hydro_branch=${{ github.event.pull_request.head.ref }}"
      - name: Move generated project
        run: |
          mv generated ${{ runner.temp }}/

      - run: cargo check --manifest-path "${{ runner.temp }}/generated/Cargo.toml" --all-targets
      - run: cargo fmt --manifest-path "${{ runner.temp }}/generated/Cargo.toml"  --all -- --check
      - run: cargo clippy --manifest-path "${{ runner.temp }}/generated/Cargo.toml"  --all --all-targets -- -D warnings
      - run: cargo test --manifest-path "${{ runner.temp }}/generated/Cargo.toml"  --all-targets --no-fail-fast
      - run: cargo build --manifest-path "${{ runner.temp }}/generated/Cargo.toml"  --all-targets

  push_dfir:
    name: Push to dfir template repo
    needs: test_dfir
    if: ${{ needs.test_dfir.result == 'success' && github.event_name != 'pull_request' }}
    env:
      DIR: template/dfir
      REPO: dfir-template
    runs-on: ubuntu-latest

    steps:
      - name: Generate token
        id: app-token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ secrets.APP_ID }}
          private-key: ${{ secrets.APP_PRIVATE_KEY }}
          owner: ${{ github.repository_owner }}
          repositories: |
            ${{ github.event.repository.name }}
            ${{ env.REPO }}

      - name: Checkout main repo
        uses: actions/checkout@v4
        with:
          token: ${{ steps.app-token.outputs.token }}

      - name: Push to template repo
        shell: bash
        run: |
          git -C "$DIR" init -q
          git -C "$DIR" remote add origin "https://github.com/${{ github.repository_owner }}/${{ env.REPO }}.git"
          git -C "$DIR" config credential.helper "$(git config credential.helper)"
          git -C "$DIR" config 'http.https://github.com/.extraheader' "$(git config 'http.https://github.com/.extraheader')"
          git -C "$DIR" config core.autocrlf input
          git -C "$DIR" config core.safecrlf false
          git -C "$DIR" config user.name '${{ steps.app-token.outputs.app-slug }}[bot]'
          git -C "$DIR" config user.email "$(gh api "/users/${{ steps.app-token.outputs.app-slug }}[bot]" --jq .id)+${{ steps.app-token.outputs.app-slug }}[bot]@users.noreply.github.com"
          git -C "$DIR" add -A
          git -C "$DIR" commit -m "Update template $(date -I) $(git rev-parse HEAD)"
          git -C "$DIR" push -f -u origin HEAD:main --quiet
        env:
          GH_TOKEN: ${{ steps.app-token.outputs.token }}

  push_hydro_lang:
    name: Push to hydro template repo
    needs: test_hydro_lang
    if: ${{ needs.test_hydro_lang.result == 'success' && github.event_name != 'pull_request' }}
    env:
      DIR: template/hydro
      REPO: hydro-template
    runs-on: ubuntu-latest

    steps:
      - name: Generate token
        id: app-token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ secrets.APP_ID }}
          private-key: ${{ secrets.APP_PRIVATE_KEY }}
          owner: ${{ github.repository_owner }}
          repositories: |
            ${{ github.event.repository.name }}
            ${{ env.REPO }}

      - name: Checkout main repo
        uses: actions/checkout@v4
        with:
          token: ${{ steps.app-token.outputs.token }}

      - name: Push to template repo
        shell: bash
        run: |
          git -C "$DIR" init -q
          git -C "$DIR" remote add origin "https://github.com/${{ github.repository_owner }}/${{ env.REPO }}.git"
          git -C "$DIR" config credential.helper "$(git config credential.helper)"
          git -C "$DIR" config 'http.https://github.com/.extraheader' "$(git config 'http.https://github.com/.extraheader')"
          git -C "$DIR" config core.autocrlf input
          git -C "$DIR" config core.safecrlf false
          git -C "$DIR" config user.name '${{ steps.app-token.outputs.app-slug }}[bot]'
          git -C "$DIR" config user.email "$(gh api "/users/${{ steps.app-token.outputs.app-slug }}[bot]" --jq .id)+${{ steps.app-token.outputs.app-slug }}[bot]@users.noreply.github.com"
          git -C "$DIR" add -A
          git -C "$DIR" commit -m "Update template $(date -I) $(git rev-parse HEAD)"
          git -C "$DIR" push -f -u origin HEAD:main --quiet
        env:
          GH_TOKEN: ${{ steps.app-token.outputs.token }}
