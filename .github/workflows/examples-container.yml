---
name: examples-container-build

on:
  push:
    branches:
      - main
      - feature/**

jobs:
  pre_job:
    runs-on: ubuntu-latest
    outputs:
      should_skip: ${{ steps.skip_check.outputs.should_skip }}
    steps:
      - id: skip_check
        uses: fkirc/skip-duplicate-actions@v5.3.1
        with:
          cancel_others: "true"
          concurrent_skipping: "same_content_newer"

  buildx:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - id: owner
        name: Lowercase OWNER
        run: |
          echo "owner=${OWNER,,}" >> "$GITHUB_OUTPUT"
        env:
          OWNER: ${{ github.repository_owner }}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2

      - id: buildx
        name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        with:
          buildkitd-flags: --debug

      - name: Login to GHCR
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push
        uses: docker/build-push-action@v3
        with:
          builder: ${{ steps.buildx.outputs.name }}
          context: .
          platforms: linux/amd64, linux/arm64
          push: true
          secrets: |
            GIT_AUTH_TOKEN=${{ github.token }}
          tags: |
            ghcr.io/${{ steps.owner.outputs.owner }}/hydroflow-examples:${{ github.sha }}
            ghcr.io/${{ steps.owner.outputs.owner }}/hydroflow-examples:latest
