<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/benches/benches" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/hydro_deploy/core/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/hydro_deploy/hydro_cli/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/hydro_deploy/hydro_cli_examples/examples" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/hydro_deploy/hydro_deploy_integration/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/hydro_deploy/hydroflow_plus_deploy/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/hydroflow/examples" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/hydroflow/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/hydroflow/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/hydroflow_datalog/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/hydroflow_datalog_core/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/hydroflow_lang/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/hydroflow_macro/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/hydroflow_plus/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/hydroflow_plus_test/examples" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/hydroflow_plus_test/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/lattices/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/lattices/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/lattices_macro/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/multiplatform_test/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/multiplatform_test/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/pusherator/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/relalg/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/topolotree/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/variadics/examples" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/variadics/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/variadics/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/website_playground/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/website_playground/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/datastores/gossip_kv/examples" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/datastores/gossip_kv/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/hydroflow_plus_test_local/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/variadics_macro/src" isTestSource="false" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>